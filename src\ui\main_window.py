#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口
"""

from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QGridLayout, QLabel, QPushButton, QMenuBar, QMenu,
                            QStatusBar, QFrame, QMessageBox, QApplication)
from PyQt6.QtCore import Qt, QTimer, pyqtSlot
from PyQt6.QtGui import QFont, QAction, QPalette, QColor
from datetime import datetime
from typing import Dict

from ..core.task_manager import TaskManager
from ..models.furnace import FurnaceStatus
from .furnace_widget import FurnaceWidget


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self, task_manager: TaskManager):
        super().__init__()
        
        self.task_manager = task_manager
        self.furnace_widgets: Dict[int, FurnaceWidget] = {}
        
        # 子窗口
        self.process_manager_window = None
        self.task_scheduler_window = None
        self.history_viewer_window = None
        self.alarm_window = None
        
        self.init_ui()
        self.connect_signals()
        
        # 定时更新界面
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(1000)  # 每秒更新一次
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("烧结炉集中控制与数据管理系统")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建中央窗口
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("烧结炉集中控制与数据管理系统")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # 炉子监控区域
        furnace_frame = QFrame()
        furnace_frame.setFrameStyle(QFrame.Shape.Box)
        furnace_layout = QGridLayout(furnace_frame)
        
        # 创建8个炉子监控组件 (2行4列)
        for i in range(1, 9):
            row = (i - 1) // 4
            col = (i - 1) % 4
            
            furnace_widget = FurnaceWidget(i, self.task_manager)
            self.furnace_widgets[i] = furnace_widget
            furnace_layout.addWidget(furnace_widget, row, col)
        
        main_layout.addWidget(furnace_frame)
        
        # 控制按钮区域
        button_layout = QHBoxLayout()
        
        self.process_btn = QPushButton("工艺库管理")
        self.process_btn.clicked.connect(self.open_process_manager)
        button_layout.addWidget(self.process_btn)
        
        self.task_btn = QPushButton("任务调度")
        self.task_btn.clicked.connect(self.open_task_scheduler)
        button_layout.addWidget(self.task_btn)
        
        self.history_btn = QPushButton("历史记录")
        self.history_btn.clicked.connect(self.open_history_viewer)
        button_layout.addWidget(self.history_btn)
        
        self.alarm_btn = QPushButton("报警记录")
        self.alarm_btn.clicked.connect(self.open_alarm_window)
        button_layout.addWidget(self.alarm_btn)
        
        button_layout.addStretch()
        
        self.exit_btn = QPushButton("退出系统")
        self.exit_btn.clicked.connect(self.close)
        button_layout.addWidget(self.exit_btn)
        
        main_layout.addLayout(button_layout)
        
        # 状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("系统已启动")
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工艺菜单
        process_menu = menubar.addMenu('工艺')
        
        process_manager_action = QAction('工艺库管理', self)
        process_manager_action.triggered.connect(self.open_process_manager)
        process_menu.addAction(process_manager_action)
        
        # 任务菜单
        task_menu = menubar.addMenu('任务')
        
        task_scheduler_action = QAction('任务调度', self)
        task_scheduler_action.triggered.connect(self.open_task_scheduler)
        task_menu.addAction(task_scheduler_action)
        
        # 查询菜单
        query_menu = menubar.addMenu('查询')
        
        history_action = QAction('历史记录', self)
        history_action.triggered.connect(self.open_history_viewer)
        query_menu.addAction(history_action)
        
        alarm_action = QAction('报警记录', self)
        alarm_action.triggered.connect(self.open_alarm_window)
        query_menu.addAction(alarm_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        
        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def connect_signals(self):
        """连接信号"""
        # 连接任务管理器信号
        self.task_manager.task_started.connect(self.on_task_started)
        self.task_manager.task_completed.connect(self.on_task_completed)
        self.task_manager.task_stopped.connect(self.on_task_stopped)
        self.task_manager.task_failed.connect(self.on_task_failed)
        self.task_manager.alarm_triggered.connect(self.on_alarm_triggered)
        self.task_manager.alarm_cleared.connect(self.on_alarm_cleared)
        
        # 连接通讯管理器信号
        self.task_manager.comm_manager.furnace_data_updated.connect(self.on_furnace_data_updated)
    
    @pyqtSlot()
    def update_display(self):
        """更新显示"""
        try:
            # 更新状态栏
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            running_count = len(self.task_manager.get_running_tasks())
            alarm_count = len(self.task_manager.get_active_alarms())
            
            status_text = f"当前时间: {current_time} | 运行任务: {running_count} | 活动报警: {alarm_count}"
            self.status_bar.showMessage(status_text)
            
        except Exception as e:
            print(f"更新显示失败: {e}")
    
    @pyqtSlot(int, object)
    def on_furnace_data_updated(self, furnace_id: int, furnace_data):
        """炉子数据更新"""
        if furnace_id in self.furnace_widgets:
            self.furnace_widgets[furnace_id].update_data(furnace_data)
    
    @pyqtSlot(str)
    def on_task_started(self, task_id: str):
        """任务开始"""
        self.status_bar.showMessage(f"任务已开始: {task_id}", 3000)
    
    @pyqtSlot(str)
    def on_task_completed(self, task_id: str):
        """任务完成"""
        self.status_bar.showMessage(f"任务已完成: {task_id}", 3000)
    
    @pyqtSlot(str)
    def on_task_stopped(self, task_id: str):
        """任务停止"""
        self.status_bar.showMessage(f"任务已停止: {task_id}", 3000)
    
    @pyqtSlot(str)
    def on_task_failed(self, task_id: str):
        """任务失败"""
        self.status_bar.showMessage(f"任务失败: {task_id}", 5000)
    
    @pyqtSlot(int, str)
    def on_alarm_triggered(self, furnace_id: int, message: str):
        """报警触发"""
        self.status_bar.showMessage(f"炉子{furnace_id}报警: {message}", 5000)
        
        # 更新炉子组件显示
        if furnace_id in self.furnace_widgets:
            self.furnace_widgets[furnace_id].set_alarm_state(True)
    
    @pyqtSlot(int, str)
    def on_alarm_cleared(self, furnace_id: int, message: str):
        """报警清除"""
        self.status_bar.showMessage(f"炉子{furnace_id}: {message}", 3000)
        
        # 更新炉子组件显示
        if furnace_id in self.furnace_widgets:
            self.furnace_widgets[furnace_id].set_alarm_state(False)
    
    def open_process_manager(self):
        """打开工艺库管理窗口"""
        from .process_manager_window import ProcessManagerWindow
        if not self.process_manager_window:
            self.process_manager_window = ProcessManagerWindow(self.task_manager)
        self.process_manager_window.show()
        self.process_manager_window.raise_()
    
    def open_task_scheduler(self):
        """打开任务调度窗口"""
        from .task_scheduler_window import TaskSchedulerWindow
        if not self.task_scheduler_window:
            self.task_scheduler_window = TaskSchedulerWindow(self.task_manager)
        self.task_scheduler_window.show()
        self.task_scheduler_window.raise_()

    def open_history_viewer(self):
        """打开历史记录窗口"""
        from .history_viewer_window import HistoryViewerWindow
        if not self.history_viewer_window:
            self.history_viewer_window = HistoryViewerWindow(self.task_manager)
        self.history_viewer_window.show()
        self.history_viewer_window.raise_()

    def open_alarm_window(self):
        """打开报警记录窗口"""
        from .alarm_window import AlarmWindow
        if not self.alarm_window:
            self.alarm_window = AlarmWindow(self.task_manager)
        self.alarm_window.show()
        self.alarm_window.raise_()
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于", 
                         "烧结炉集中控制与数据管理系统 v1.0\n\n"
                         "基于Python和PyQt6开发\n"
                         "支持8台烧结炉的集中监控和控制")
    
    def closeEvent(self, event):
        """关闭事件"""
        reply = QMessageBox.question(self, '确认退出', 
                                   '确定要退出系统吗？',
                                   QMessageBox.StandardButton.Yes | 
                                   QMessageBox.StandardButton.No,
                                   QMessageBox.StandardButton.No)
        
        if reply == QMessageBox.StandardButton.Yes:
            # 清理资源
            self.update_timer.stop()
            event.accept()
        else:
            event.ignore()
