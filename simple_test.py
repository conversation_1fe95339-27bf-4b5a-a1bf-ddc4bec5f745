#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from src.models.process import Process, ProcessStep
        print("✓ 工艺模型导入成功")
    except Exception as e:
        print(f"✗ 工艺模型导入失败: {e}")
        return False
    
    try:
        from src.models.task import Task, TaskStatus
        print("✓ 任务模型导入成功")
    except Exception as e:
        print(f"✗ 任务模型导入失败: {e}")
        return False
    
    try:
        from src.models.furnace import FurnaceData, FurnaceStatus
        print("✓ 炉子模型导入成功")
    except Exception as e:
        print(f"✗ 炉子模型导入失败: {e}")
        return False
    
    try:
        from src.data.database_manager import DatabaseManager
        print("✓ 数据库管理器导入成功")
    except Exception as e:
        print(f"✗ 数据库管理器导入失败: {e}")
        return False
    
    try:
        from src.communication.aibus_protocol import AIBUSProtocol
        print("✓ AIBUS协议导入成功")
    except Exception as e:
        print(f"✗ AIBUS协议导入失败: {e}")
        return False
    
    try:
        from src.communication.communication_manager import CommunicationManager
        print("✓ 通讯管理器导入成功")
    except Exception as e:
        print(f"✗ 通讯管理器导入失败: {e}")
        return False
    
    try:
        from src.core.task_manager import TaskManager
        print("✓ 任务管理器导入成功")
    except Exception as e:
        print(f"✗ 任务管理器导入失败: {e}")
        return False
    
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")
    
    try:
        from src.models.process import Process
        from src.data.database_manager import DatabaseManager
        from datetime import datetime
        
        # 测试数据库初始化
        db_manager = DatabaseManager("test_simple.db")
        db_manager.initialize_database()
        print("✓ 数据库初始化成功")
        
        # 测试工艺创建
        process = Process(
            process_id=None,
            name="测试工艺",
            description="简单测试工艺"
        )
        process.add_step(100.0, 5.0)
        process.add_step(200.0, 3.0)
        
        process_id = db_manager.save_process(process)
        print(f"✓ 工艺保存成功: {process_id}")
        
        # 测试工艺读取
        loaded_process = db_manager.get_process(process_id)
        if loaded_process and loaded_process.name == "测试工艺":
            print("✓ 工艺读取成功")
        else:
            print("✗ 工艺读取失败")
            return False
        
        # 清理测试文件
        if os.path.exists("test_simple.db"):
            os.remove("test_simple.db")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def test_gui_components():
    """测试GUI组件"""
    print("\n测试GUI组件...")
    
    try:
        # 检查PyQt6是否可用
        try:
            from PyQt6.QtWidgets import QApplication
            print("✓ PyQt6可用")
        except ImportError:
            print("✗ PyQt6不可用，跳过GUI测试")
            return True
        
        # 测试UI组件导入
        from src.ui.furnace_widget import FurnaceWidget
        print("✓ 炉子组件导入成功")
        
        from src.ui.main_window import MainWindow
        print("✓ 主窗口导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ GUI组件测试失败: {e}")
        return False

def main():
    """主函数"""
    print("烧结炉控制系统 - 简单测试")
    print("=" * 40)
    
    success = True
    
    # 测试模块导入
    success &= test_imports()
    
    # 测试基本功能
    success &= test_basic_functionality()
    
    # 测试GUI组件
    success &= test_gui_components()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 所有测试通过！")
        print("\n系统组件已正确安装和配置。")
        print("要启动完整系统，请运行: python main.py")
        print("\n注意：")
        print("1. 确保已安装所有依赖包: pip install -r requirements.txt")
        print("2. 系统需要串口设备才能正常通讯")
        print("3. 首次运行会创建数据库文件")
    else:
        print("❌ 测试失败！")
        print("请检查错误信息并修复问题。")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
