#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
历史记录查看窗口
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QComboBox, QDateEdit, QGroupBox, QGridLayout,
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QFileDialog, QCheckBox)
from PyQt6.QtCore import Qt, QDate, pyqtSlot
from PyQt6.QtGui import QFont
from datetime import datetime, timedelta

from ..core.task_manager import TaskManager
from ..models.task import TaskStatus
from .task_detail_window import TaskDetailWindow


class HistoryViewerWindow(QDialog):
    """历史记录查看窗口"""
    
    def __init__(self, task_manager: TaskManager):
        super().__init__()
        
        self.task_manager = task_manager
        self.selected_tasks = []
        
        self.init_ui()
        self.load_default_data()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("历史记录查询")
        self.setGeometry(100, 100, 1200, 700)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("历史记录查询")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 查询条件组
        query_group = QGroupBox("查询条件")
        query_layout = QGridLayout(query_group)
        
        # 日期范围
        query_layout.addWidget(QLabel("开始日期:"), 0, 0)
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-7))  # 默认最近7天
        self.start_date_edit.setCalendarPopup(True)
        query_layout.addWidget(self.start_date_edit, 0, 1)
        
        query_layout.addWidget(QLabel("结束日期:"), 0, 2)
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        query_layout.addWidget(self.end_date_edit, 0, 3)
        
        # 炉子选择
        query_layout.addWidget(QLabel("炉子:"), 1, 0)
        self.furnace_combo = QComboBox()
        self.furnace_combo.addItem("全部", None)
        for i in range(1, 9):
            self.furnace_combo.addItem(f"{i}号炉", i)
        query_layout.addWidget(self.furnace_combo, 1, 1)
        
        # 状态选择
        query_layout.addWidget(QLabel("状态:"), 1, 2)
        self.status_combo = QComboBox()
        self.status_combo.addItem("全部", None)
        for status in TaskStatus:
            self.status_combo.addItem(status.value, status)
        query_layout.addWidget(self.status_combo, 1, 3)
        
        # 查询按钮
        self.query_btn = QPushButton("查询")
        self.query_btn.clicked.connect(self.query_history)
        query_layout.addWidget(self.query_btn, 2, 0, 1, 4)
        
        layout.addWidget(query_group)
        
        # 结果表格
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(8)
        self.result_table.setHorizontalHeaderLabels([
            "选择", "任务ID", "工艺名称", "炉子", "状态", 
            "开始时间", "结束时间", "持续时间"
        ])
        
        # 设置表格属性
        self.result_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.result_table.setAlternatingRowColors(True)
        
        # 设置列宽
        header = self.result_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)
        
        # 连接双击事件
        self.result_table.itemDoubleClicked.connect(self.show_task_detail)
        
        layout.addWidget(self.result_table)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.clicked.connect(self.select_all)
        button_layout.addWidget(self.select_all_btn)
        
        self.select_none_btn = QPushButton("全不选")
        self.select_none_btn.clicked.connect(self.select_none)
        button_layout.addWidget(self.select_none_btn)
        
        self.export_selected_btn = QPushButton("导出选中")
        self.export_selected_btn.clicked.connect(self.export_selected)
        button_layout.addWidget(self.export_selected_btn)
        
        self.view_detail_btn = QPushButton("查看详情")
        self.view_detail_btn.clicked.connect(self.view_selected_detail)
        button_layout.addWidget(self.view_detail_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def load_default_data(self):
        """加载默认数据"""
        self.query_history()
    
    @pyqtSlot()
    def query_history(self):
        """查询历史记录"""
        try:
            # 获取查询条件
            start_date = self.start_date_edit.date().toPython()
            end_date = self.end_date_edit.date().toPython()
            
            # 转换为datetime
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())
            
            furnace_id = self.furnace_combo.currentData()
            status = self.status_combo.currentData()
            
            # 查询数据
            tasks = self.task_manager.db_manager.get_task_history(
                furnace_id=furnace_id,
                start_date=start_datetime,
                end_date=end_datetime,
                status=status
            )
            
            # 填充表格
            self.fill_table(tasks)
            
        except Exception as e:
            print(f"查询历史记录失败: {e}")
            QMessageBox.warning(self, "错误", f"查询失败: {e}")
    
    def fill_table(self, tasks):
        """填充表格"""
        try:
            self.result_table.setRowCount(len(tasks))
            self.selected_tasks = []
            
            for row, task in enumerate(tasks):
                # 选择框
                checkbox = QCheckBox()
                checkbox.stateChanged.connect(lambda state, t=task: self.on_task_selected(state, t))
                self.result_table.setCellWidget(row, 0, checkbox)
                
                # 任务ID
                task_id_item = QTableWidgetItem(task.task_id[:8] + "..." if len(task.task_id) > 8 else task.task_id)
                task_id_item.setData(Qt.ItemDataRole.UserRole, task.task_id)
                self.result_table.setItem(row, 1, task_id_item)
                
                # 工艺名称
                process = self.task_manager.db_manager.get_process(task.process_id)
                process_name = process.name if process else "未知工艺"
                self.result_table.setItem(row, 2, QTableWidgetItem(process_name))
                
                # 炉子
                self.result_table.setItem(row, 3, QTableWidgetItem(f"{task.furnace_id}号炉"))
                
                # 状态
                status_item = QTableWidgetItem(task.status.value)
                # 根据状态设置颜色
                if task.status == TaskStatus.COMPLETED:
                    status_item.setBackground(Qt.GlobalColor.lightGreen)
                elif task.status == TaskStatus.STOPPED:
                    status_item.setBackground(Qt.GlobalColor.yellow)
                elif task.status == TaskStatus.FAILED:
                    status_item.setBackground(Qt.GlobalColor.red)
                
                self.result_table.setItem(row, 4, status_item)
                
                # 开始时间
                start_time = ""
                if task.actual_start_time:
                    start_time = task.actual_start_time.strftime("%Y-%m-%d %H:%M:%S")
                self.result_table.setItem(row, 5, QTableWidgetItem(start_time))
                
                # 结束时间
                end_time = ""
                if task.actual_end_time:
                    end_time = task.actual_end_time.strftime("%Y-%m-%d %H:%M:%S")
                self.result_table.setItem(row, 6, QTableWidgetItem(end_time))
                
                # 持续时间
                duration = ""
                if task.actual_start_time and task.actual_end_time:
                    delta = task.actual_end_time - task.actual_start_time
                    hours, remainder = divmod(int(delta.total_seconds()), 3600)
                    minutes, seconds = divmod(remainder, 60)
                    duration = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                
                self.result_table.setItem(row, 7, QTableWidgetItem(duration))
            
        except Exception as e:
            print(f"填充表格失败: {e}")
    
    def on_task_selected(self, state, task):
        """任务选择状态改变"""
        if state == Qt.CheckState.Checked.value:
            if task not in self.selected_tasks:
                self.selected_tasks.append(task)
        else:
            if task in self.selected_tasks:
                self.selected_tasks.remove(task)
    
    @pyqtSlot()
    def select_all(self):
        """全选"""
        for row in range(self.result_table.rowCount()):
            checkbox = self.result_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(True)
    
    @pyqtSlot()
    def select_none(self):
        """全不选"""
        for row in range(self.result_table.rowCount()):
            checkbox = self.result_table.cellWidget(row, 0)
            if checkbox:
                checkbox.setChecked(False)
    
    @pyqtSlot()
    def export_selected(self):
        """导出选中的任务"""
        try:
            if not self.selected_tasks:
                QMessageBox.warning(self, "警告", "请选择要导出的任务")
                return
            
            # 选择导出目录
            export_dir = QFileDialog.getExistingDirectory(self, "选择导出目录")
            if not export_dir:
                return
            
            # 导出每个选中的任务
            success_count = 0
            for task in self.selected_tasks:
                filename = f"task_{task.task_id}_{task.actual_start_time.strftime('%Y%m%d_%H%M%S') if task.actual_start_time else 'unknown'}.csv"
                filepath = f"{export_dir}/{filename}"
                
                if self.task_manager.db_manager.export_task_data_to_csv(task.task_id, filepath):
                    success_count += 1
            
            QMessageBox.information(self, "导出完成", 
                                  f"成功导出 {success_count}/{len(self.selected_tasks)} 个任务")
            
        except Exception as e:
            print(f"导出任务失败: {e}")
            QMessageBox.warning(self, "错误", f"导出失败: {e}")
    
    @pyqtSlot()
    def view_selected_detail(self):
        """查看选中任务的详情"""
        try:
            current_row = self.result_table.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "警告", "请选择一个任务")
                return
            
            self.show_task_detail_by_row(current_row)
            
        except Exception as e:
            print(f"查看详情失败: {e}")
    
    @pyqtSlot(object)
    def show_task_detail(self, item):
        """显示任务详情"""
        try:
            row = item.row()
            self.show_task_detail_by_row(row)
            
        except Exception as e:
            print(f"显示任务详情失败: {e}")
    
    def show_task_detail_by_row(self, row):
        """根据行号显示任务详情"""
        try:
            task_id_item = self.result_table.item(row, 1)
            if not task_id_item:
                return
            
            task_id = task_id_item.data(Qt.ItemDataRole.UserRole)
            task = self.task_manager.db_manager.get_task(task_id)
            
            if task:
                # 创建任务详情窗口
                detail_window = TaskDetailWindow(task.furnace_id, self.task_manager)
                detail_window.current_task = task
                detail_window.update_data()
                detail_window.exec()
            
        except Exception as e:
            print(f"显示任务详情失败: {e}")
