#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
烧结炉集中控制与数据管理系统
主程序入口
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTranslator, QLocale
from src.ui.main_window import MainWindow
from src.core.task_manager import TaskManager
from src.data.database_manager import DatabaseManager
from src.communication.communication_manager import CommunicationManager


def main():
    """主程序入口"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("烧结炉控制系统")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("工业控制系统")
    
    # 初始化数据库
    db_manager = DatabaseManager()
    db_manager.initialize_database()
    
    # 初始化通讯管理器
    comm_manager = CommunicationManager()
    
    # 初始化任务管理器
    task_manager = TaskManager(db_manager, comm_manager)
    
    # 创建主窗口
    main_window = MainWindow(task_manager)
    main_window.show()
    
    # 启动通讯
    comm_manager.start_communication()
    
    # 运行应用程序
    try:
        sys.exit(app.exec())
    finally:
        # 清理资源
        comm_manager.stop_communication()
        task_manager.cleanup()


if __name__ == "__main__":
    main()
