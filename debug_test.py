#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试测试脚本 - 逐步检查各个组件
"""

import sys
import os
import traceback

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_step_by_step():
    """逐步测试各个组件"""
    print("开始逐步测试...")
    print("=" * 50)
    
    # 步骤1: 测试基础导入
    print("步骤1: 测试基础导入")
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QTimer, Qt
        print("✓ PyQt6基础组件导入成功")
    except Exception as e:
        print(f"✗ PyQt6导入失败: {e}")
        return False
    
    # 步骤2: 测试数据模型
    print("\n步骤2: 测试数据模型")
    try:
        from src.models.furnace import FurnaceData, FurnaceStatus
        from src.models.process import Process, ProcessStep
        from src.models.task import Task, TaskStatus
        print("✓ 数据模型导入成功")
        
        # 测试创建对象
        furnace_data = FurnaceData(1, FurnaceStatus.IDLE, 25.0, 20.0)
        process = Process(None, "测试工艺", "测试描述")
        print("✓ 数据模型对象创建成功")
    except Exception as e:
        print(f"✗ 数据模型测试失败: {e}")
        traceback.print_exc()
        return False
    
    # 步骤3: 测试数据库管理器
    print("\n步骤3: 测试数据库管理器")
    try:
        from src.data.database_manager import DatabaseManager
        db_manager = DatabaseManager("debug_test.db")
        db_manager.initialize_database()
        print("✓ 数据库管理器初始化成功")
        
        # 清理测试数据库
        if os.path.exists("debug_test.db"):
            os.remove("debug_test.db")
    except Exception as e:
        print(f"✗ 数据库管理器测试失败: {e}")
        traceback.print_exc()
        return False
    
    # 步骤4: 测试通讯管理器
    print("\n步骤4: 测试通讯管理器")
    try:
        from src.communication.communication_manager import CommunicationManager
        comm_manager = CommunicationManager()
        print("✓ 通讯管理器创建成功")
        
        # 测试启动通讯（应该进入模拟模式）
        result = comm_manager.start_communication()
        print(f"✓ 通讯启动结果: {result}")
        
        # 停止通讯
        comm_manager.stop_communication()
        print("✓ 通讯停止成功")
    except Exception as e:
        print(f"✗ 通讯管理器测试失败: {e}")
        traceback.print_exc()
        return False
    
    # 步骤5: 测试任务管理器
    print("\n步骤5: 测试任务管理器")
    try:
        # 重新创建组件
        db_manager = DatabaseManager("debug_test.db")
        db_manager.initialize_database()
        comm_manager = CommunicationManager()
        
        from src.core.task_manager import TaskManager
        task_manager = TaskManager(db_manager, comm_manager)
        print("✓ 任务管理器创建成功")
        
        # 清理
        task_manager.cleanup()
        if os.path.exists("debug_test.db"):
            os.remove("debug_test.db")
    except Exception as e:
        print(f"✗ 任务管理器测试失败: {e}")
        traceback.print_exc()
        return False
    
    # 步骤6: 测试GUI组件
    print("\n步骤6: 测试GUI组件")
    try:
        app = QApplication([])
        
        # 测试炉子组件
        from src.ui.furnace_widget import FurnaceWidget
        
        # 重新创建组件
        db_manager = DatabaseManager("debug_test.db")
        db_manager.initialize_database()
        comm_manager = CommunicationManager()
        task_manager = TaskManager(db_manager, comm_manager)
        
        furnace_widget = FurnaceWidget(1, task_manager)
        print("✓ 炉子组件创建成功")
        
        # 清理
        task_manager.cleanup()
        if os.path.exists("debug_test.db"):
            os.remove("debug_test.db")
            
    except Exception as e:
        print(f"✗ GUI组件测试失败: {e}")
        traceback.print_exc()
        return False
    
    # 步骤7: 测试主窗口
    print("\n步骤7: 测试主窗口")
    try:
        # 重新创建组件
        db_manager = DatabaseManager("debug_test.db")
        db_manager.initialize_database()
        comm_manager = CommunicationManager()
        task_manager = TaskManager(db_manager, comm_manager)
        
        from src.ui.main_window import MainWindow
        main_window = MainWindow(task_manager)
        print("✓ 主窗口创建成功")
        
        # 显示窗口（但不进入事件循环）
        main_window.show()
        print("✓ 主窗口显示成功")
        
        # 立即关闭
        main_window.close()
        
        # 清理
        task_manager.cleanup()
        if os.path.exists("debug_test.db"):
            os.remove("debug_test.db")
            
    except Exception as e:
        print(f"✗ 主窗口测试失败: {e}")
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 50)
    print("✓ 所有组件测试通过！")
    return True


def test_full_startup():
    """测试完整启动流程"""
    print("\n测试完整启动流程...")
    print("=" * 50)
    
    try:
        app = QApplication([])
        
        # 完整初始化流程
        from src.data.database_manager import DatabaseManager
        from src.communication.communication_manager import CommunicationManager
        from src.core.task_manager import TaskManager
        from src.ui.main_window import MainWindow
        
        print("正在初始化数据库...")
        db_manager = DatabaseManager("debug_full_test.db")
        db_manager.initialize_database()
        
        print("正在初始化通讯管理器...")
        comm_manager = CommunicationManager()
        
        print("正在初始化任务管理器...")
        task_manager = TaskManager(db_manager, comm_manager)
        
        print("正在创建主窗口...")
        main_window = MainWindow(task_manager)
        
        print("正在显示主窗口...")
        main_window.show()
        
        print("正在启动通讯...")
        comm_manager.start_communication()
        
        print("✓ 完整启动成功！")
        print("窗口将显示3秒后自动关闭...")
        
        # 使用QTimer在3秒后关闭
        from PyQt6.QtCore import QTimer
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(3000)  # 3秒
        
        # 运行事件循环
        app.exec()
        
        # 清理
        comm_manager.stop_communication()
        task_manager.cleanup()
        if os.path.exists("debug_full_test.db"):
            os.remove("debug_full_test.db")
        
        print("✓ 完整测试成功！")
        return True
        
    except Exception as e:
        print(f"✗ 完整启动测试失败: {e}")
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("烧结炉控制系统 - 调试测试")
    print("=" * 50)
    
    # 逐步测试
    if test_step_by_step():
        # 完整启动测试
        test_full_startup()
    else:
        print("逐步测试失败，跳过完整测试")
        sys.exit(1)
    
    print("\n调试测试完成！")
