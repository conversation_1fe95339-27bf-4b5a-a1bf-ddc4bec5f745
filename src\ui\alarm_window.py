#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报警记录窗口
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem,
                            QComboBox, QDateEdit, QGroupBox, QGridLayout,
                            QMessageBox, QHeaderView, QAbstractItemView,
                            QTabWidget, QWidget)
from PyQt6.QtCore import Qt, QDate, pyqtSlot, QTimer
from PyQt6.QtGui import QFont
from datetime import datetime, timedelta

from ..core.task_manager import TaskManager


class AlarmWindow(QDialog):
    """报警记录窗口"""
    
    def __init__(self, task_manager: TaskManager):
        super().__init__()
        
        self.task_manager = task_manager
        
        self.init_ui()
        self.load_data()
        
        # 定时刷新活动报警
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_active_alarms)
        self.refresh_timer.start(5000)  # 每5秒刷新一次
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("报警记录")
        self.setGeometry(100, 100, 1000, 600)
        
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("报警记录管理")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 活动报警标签页
        self.active_tab = self.create_active_alarms_tab()
        self.tab_widget.addTab(self.active_tab, "活动报警")
        
        # 历史报警标签页
        self.history_tab = self.create_history_alarms_tab()
        self.tab_widget.addTab(self.history_tab, "历史报警")
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_data)
        button_layout.addWidget(self.refresh_btn)
        
        self.clear_all_btn = QPushButton("清除所有报警")
        self.clear_all_btn.clicked.connect(self.clear_all_alarms)
        button_layout.addWidget(self.clear_all_btn)
        
        button_layout.addStretch()
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def create_active_alarms_tab(self):
        """创建活动报警标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 活动报警表格
        self.active_table = QTableWidget()
        self.active_table.setColumnCount(5)
        self.active_table.setHorizontalHeaderLabels([
            "炉子", "报警类型", "报警信息", "开始时间", "持续时间"
        ])
        
        # 设置表格属性
        self.active_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.active_table.setAlternatingRowColors(True)
        
        # 设置列宽
        active_header = self.active_table.horizontalHeader()
        active_header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        active_header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        active_header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        active_header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        active_header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        
        layout.addWidget(self.active_table)
        
        return widget
    
    def create_history_alarms_tab(self):
        """创建历史报警标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 查询条件组
        query_group = QGroupBox("查询条件")
        query_layout = QGridLayout(query_group)
        
        # 日期范围
        query_layout.addWidget(QLabel("开始日期:"), 0, 0)
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate.currentDate().addDays(-7))  # 默认最近7天
        self.start_date_edit.setCalendarPopup(True)
        query_layout.addWidget(self.start_date_edit, 0, 1)
        
        query_layout.addWidget(QLabel("结束日期:"), 0, 2)
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.setCalendarPopup(True)
        query_layout.addWidget(self.end_date_edit, 0, 3)
        
        # 炉子选择
        query_layout.addWidget(QLabel("炉子:"), 1, 0)
        self.furnace_combo = QComboBox()
        self.furnace_combo.addItem("全部", None)
        for i in range(1, 9):
            self.furnace_combo.addItem(f"{i}号炉", i)
        query_layout.addWidget(self.furnace_combo, 1, 1)
        
        # 查询按钮
        self.query_btn = QPushButton("查询")
        self.query_btn.clicked.connect(self.query_history_alarms)
        query_layout.addWidget(self.query_btn, 1, 2, 1, 2)
        
        layout.addWidget(query_group)
        
        # 历史报警表格
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(6)
        self.history_table.setHorizontalHeaderLabels([
            "炉子", "报警类型", "报警信息", "开始时间", "结束时间", "持续时间"
        ])
        
        # 设置表格属性
        self.history_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.history_table.setAlternatingRowColors(True)
        
        # 设置列宽
        history_header = self.history_table.horizontalHeader()
        history_header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        history_header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        history_header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)
        history_header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        history_header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)
        history_header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)
        
        layout.addWidget(self.history_table)
        
        return widget
    
    def load_data(self):
        """加载数据"""
        self.load_active_alarms()
        self.load_history_alarms()
    
    def load_active_alarms(self):
        """加载活动报警"""
        try:
            active_alarms = self.task_manager.get_active_alarms()
            
            self.active_table.setRowCount(len(active_alarms))
            
            for row, alarm in enumerate(active_alarms):
                # 炉子
                self.active_table.setItem(row, 0, QTableWidgetItem(f"{alarm.furnace_id}号炉"))
                
                # 报警类型
                type_item = QTableWidgetItem(alarm.alarm_type)
                # 根据类型设置颜色
                if alarm.alarm_type == "通讯中断":
                    type_item.setBackground(Qt.GlobalColor.gray)
                elif alarm.alarm_type == "温度超差":
                    type_item.setBackground(Qt.GlobalColor.yellow)
                elif alarm.alarm_type == "设备故障":
                    type_item.setBackground(Qt.GlobalColor.red)
                
                self.active_table.setItem(row, 1, type_item)
                
                # 报警信息
                self.active_table.setItem(row, 2, QTableWidgetItem(alarm.alarm_message))
                
                # 开始时间
                start_time = alarm.start_time.strftime("%Y-%m-%d %H:%M:%S")
                self.active_table.setItem(row, 3, QTableWidgetItem(start_time))
                
                # 持续时间
                duration = datetime.now() - alarm.start_time
                hours, remainder = divmod(int(duration.total_seconds()), 3600)
                minutes, seconds = divmod(remainder, 60)
                duration_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                self.active_table.setItem(row, 4, QTableWidgetItem(duration_str))
            
        except Exception as e:
            print(f"加载活动报警失败: {e}")
    
    def load_history_alarms(self):
        """加载历史报警"""
        try:
            # 获取查询条件
            start_date = self.start_date_edit.date().toPython()
            end_date = self.end_date_edit.date().toPython()
            
            # 转换为datetime
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())
            
            furnace_id = self.furnace_combo.currentData()
            
            # 查询历史报警
            history_alarms = self.task_manager.db_manager.get_alarm_history(
                furnace_id=furnace_id,
                start_date=start_datetime,
                end_date=end_datetime
            )
            
            self.history_table.setRowCount(len(history_alarms))
            
            for row, alarm in enumerate(history_alarms):
                # 炉子
                self.history_table.setItem(row, 0, QTableWidgetItem(f"{alarm.furnace_id}号炉"))
                
                # 报警类型
                type_item = QTableWidgetItem(alarm.alarm_type)
                # 根据类型设置颜色
                if alarm.alarm_type == "通讯中断":
                    type_item.setBackground(Qt.GlobalColor.gray)
                elif alarm.alarm_type == "温度超差":
                    type_item.setBackground(Qt.GlobalColor.yellow)
                elif alarm.alarm_type == "设备故障":
                    type_item.setBackground(Qt.GlobalColor.red)
                
                self.history_table.setItem(row, 1, type_item)
                
                # 报警信息
                self.history_table.setItem(row, 2, QTableWidgetItem(alarm.alarm_message))
                
                # 开始时间
                start_time = alarm.start_time.strftime("%Y-%m-%d %H:%M:%S")
                self.history_table.setItem(row, 3, QTableWidgetItem(start_time))
                
                # 结束时间
                end_time = ""
                if alarm.end_time:
                    end_time = alarm.end_time.strftime("%Y-%m-%d %H:%M:%S")
                self.history_table.setItem(row, 4, QTableWidgetItem(end_time))
                
                # 持续时间
                duration_str = ""
                if alarm.end_time:
                    duration = alarm.end_time - alarm.start_time
                    hours, remainder = divmod(int(duration.total_seconds()), 3600)
                    minutes, seconds = divmod(remainder, 60)
                    duration_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                
                self.history_table.setItem(row, 5, QTableWidgetItem(duration_str))
            
        except Exception as e:
            print(f"加载历史报警失败: {e}")
    
    @pyqtSlot()
    def query_history_alarms(self):
        """查询历史报警"""
        self.load_history_alarms()
    
    @pyqtSlot()
    def refresh_active_alarms(self):
        """刷新活动报警"""
        if self.tab_widget.currentIndex() == 0:  # 活动报警标签页
            self.load_active_alarms()
    
    @pyqtSlot()
    def refresh_data(self):
        """刷新数据"""
        self.load_data()
    
    @pyqtSlot()
    def clear_all_alarms(self):
        """清除所有报警"""
        try:
            reply = QMessageBox.question(self, '确认清除', 
                                       '确定要清除所有活动报警吗？',
                                       QMessageBox.StandardButton.Yes | 
                                       QMessageBox.StandardButton.No,
                                       QMessageBox.StandardButton.No)
            
            if reply == QMessageBox.StandardButton.Yes:
                # 这里可以实现清除所有报警的逻辑
                QMessageBox.information(self, "提示", "清除报警功能待实现")
            
        except Exception as e:
            print(f"清除报警失败: {e}")
    
    def closeEvent(self, event):
        """关闭事件"""
        self.refresh_timer.stop()
        event.accept()
