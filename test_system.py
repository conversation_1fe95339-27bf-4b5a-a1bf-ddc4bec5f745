#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本
"""

import sys
import os
import unittest
from datetime import datetime, timedelta

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.data.database_manager import DatabaseManager
from src.models.process import Process, ProcessStep
from src.models.task import Task, TaskStatus
from src.models.furnace import FurnaceData, FurnaceStatus


class TestDatabaseManager(unittest.TestCase):
    """测试数据库管理器"""
    
    def setUp(self):
        """设置测试环境"""
        self.db_manager = DatabaseManager("test.db")
        self.db_manager.initialize_database()
    
    def tearDown(self):
        """清理测试环境"""
        if os.path.exists("test.db"):
            os.remove("test.db")
    
    def test_process_operations(self):
        """测试工艺操作"""
        # 创建工艺
        process = Process(
            process_id=None,
            name="测试工艺",
            description="这是一个测试工艺"
        )
        
        # 添加步骤
        process.add_step(100.0, 5.0)
        process.add_step(200.0, 3.0)
        process.add_step(150.0, 2.0)
        
        # 保存工艺
        process_id = self.db_manager.save_process(process)
        self.assertIsNotNone(process_id)
        
        # 读取工艺
        loaded_process = self.db_manager.get_process(process_id)
        self.assertIsNotNone(loaded_process)
        self.assertEqual(loaded_process.name, "测试工艺")
        self.assertEqual(len(loaded_process.steps), 3)
        
        # 获取所有工艺
        all_processes = self.db_manager.get_all_processes()
        self.assertEqual(len(all_processes), 1)
        
        print("✓ 工艺操作测试通过")
    
    def test_task_operations(self):
        """测试任务操作"""
        # 先创建工艺
        process = Process(
            process_id=None,
            name="测试工艺",
            description="测试用工艺"
        )
        process.add_step(100.0, 5.0)
        process_id = self.db_manager.save_process(process)
        
        # 创建任务
        task = Task(
            task_id=None,
            process_id=process_id,
            furnace_id=1,
            status=TaskStatus.PENDING,
            scheduled_start_time=datetime.now()
        )
        
        # 保存任务
        task_id = self.db_manager.save_task(task)
        self.assertIsNotNone(task_id)
        
        # 读取任务
        loaded_task = self.db_manager.get_task(task_id)
        self.assertIsNotNone(loaded_task)
        self.assertEqual(loaded_task.furnace_id, 1)
        self.assertEqual(loaded_task.status, TaskStatus.PENDING)
        
        # 更新任务状态
        self.db_manager.update_task_status(task_id, TaskStatus.RUNNING)
        updated_task = self.db_manager.get_task(task_id)
        self.assertEqual(updated_task.status, TaskStatus.RUNNING)
        
        # 保存温度记录
        self.db_manager.save_temperature_record(task_id, 100.0, 95.0, 1)
        self.db_manager.save_temperature_record(task_id, 100.0, 98.0, 1)
        
        # 重新读取任务，检查温度记录
        task_with_records = self.db_manager.get_task(task_id)
        self.assertEqual(len(task_with_records.temperature_records), 2)
        
        print("✓ 任务操作测试通过")


class TestProcessModel(unittest.TestCase):
    """测试工艺模型"""
    
    def test_process_steps(self):
        """测试工艺步骤"""
        process = Process(
            process_id="test_process",
            name="测试工艺",
            description="测试描述"
        )
        
        # 添加步骤
        step1 = process.add_step(100.0, 5.0)
        step2 = process.add_step(200.0, 3.0)
        step3 = process.add_step(150.0, 2.0)
        
        self.assertEqual(len(process.steps), 3)
        self.assertEqual(step1.step_id, 1)
        self.assertEqual(step2.step_id, 2)
        self.assertEqual(step3.step_id, 3)
        
        # 删除步骤
        success = process.remove_step(2)
        self.assertTrue(success)
        self.assertEqual(len(process.steps), 2)
        
        # 检查步骤重新编号
        self.assertEqual(process.steps[0].step_id, 1)
        self.assertEqual(process.steps[1].step_id, 2)
        self.assertEqual(process.steps[1].target_temp, 150.0)
        
        print("✓ 工艺步骤测试通过")
    
    def test_duration_calculation(self):
        """测试时间计算"""
        process = Process(
            process_id="test_process",
            name="测试工艺",
            description="测试描述"
        )
        
        process.add_step(100.0, 5.0)  # 从20度到100度，5度/分钟 = 16分钟
        process.add_step(200.0, 10.0)  # 从100度到200度，10度/分钟 = 10分钟
        
        total_duration = process.get_total_duration(20.0)
        expected_duration = 16 + 10  # 26分钟
        self.assertEqual(total_duration, expected_duration)
        
        print("✓ 时间计算测试通过")


def run_basic_tests():
    """运行基础测试"""
    print("开始运行系统测试...")
    print("=" * 50)
    
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试用例
    suite.addTest(TestProcessModel('test_process_steps'))
    suite.addTest(TestProcessModel('test_duration_calculation'))
    suite.addTest(TestDatabaseManager('test_process_operations'))
    suite.addTest(TestDatabaseManager('test_task_operations'))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=0)
    result = runner.run(suite)
    
    print("=" * 50)
    if result.wasSuccessful():
        print("✓ 所有测试通过！")
        return True
    else:
        print("✗ 测试失败！")
        for failure in result.failures:
            print(f"失败: {failure[0]}")
            print(f"错误: {failure[1]}")
        for error in result.errors:
            print(f"错误: {error[0]}")
            print(f"详情: {error[1]}")
        return False


def test_gui_startup():
    """测试GUI启动"""
    print("\n测试GUI启动...")
    try:
        from PyQt6.QtWidgets import QApplication
        from src.ui.main_window import MainWindow
        from src.core.task_manager import TaskManager
        from src.data.database_manager import DatabaseManager
        from src.communication.communication_manager import CommunicationManager
        
        # 创建应用程序（不显示窗口）
        app = QApplication([])
        
        # 初始化组件
        db_manager = DatabaseManager("test_gui.db")
        db_manager.initialize_database()
        
        comm_manager = CommunicationManager()
        task_manager = TaskManager(db_manager, comm_manager)
        
        # 创建主窗口
        main_window = MainWindow(task_manager)
        
        # 清理
        task_manager.cleanup()
        if os.path.exists("test_gui.db"):
            os.remove("test_gui.db")
        
        print("✓ GUI启动测试通过")
        return True
        
    except Exception as e:
        print(f"✗ GUI启动测试失败: {e}")
        return False


if __name__ == "__main__":
    success = True
    
    # 运行基础测试
    success &= run_basic_tests()
    
    # 测试GUI启动
    success &= test_gui_startup()
    
    if success:
        print("\n🎉 所有测试通过！系统可以正常运行。")
        print("\n要启动系统，请运行: python main.py")
    else:
        print("\n❌ 测试失败，请检查错误信息。")
        sys.exit(1)
